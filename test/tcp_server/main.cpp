#include <chrono>
#include <format>
#include <functional>
#include <iostream>
#include <set>
#include <thread>
#include <vector>


#include <asio.hpp>

#include "logger.hpp"
#include "tcp_server.hpp"

constexpr int cacheSize = 1024;
using TcpServ = TCPServer<1, cacheSize>;

using asio::steady_timer;
using asio::ip::tcp;

int main(int argc, char *argv[])
{
    std::string msg = "hello world!";

    asio::io_context ioContext;
    TcpServ server(
        ioContext, 6868,
        [&msg](TcpServ::SSPtr s) {
            Logger::Get().info("client connected");
            s->read(msg.size());
            s->write(std::vector<uint8_t>(msg.begin(), msg.end()));
        },
        [](TcpServ::SSPtr s) { Logger::Get().info("client disconnected"); },
        [&msg](TcpServ::SSPtr s, std::vector<uint8_t> &&data) {
            Logger::Get().info(
                std::format("read: {}", std::string_view((char *) data.data(), data.size())));
            s->read(msg.size());
        },
        [&msg, &ioContext](TcpServ::SSPtr s) {
            Logger::Get().info("wrote");
            auto timer = std::make_shared<steady_timer>(ioContext, std::chrono::seconds(1));
            timer->async_wait([s, &msg, timer](const asio::error_code& /*e*/) {
                s->write(std::vector<uint8_t>(msg.begin(), msg.end()));
            });
        });

    std::thread([&server]() {
        while (true) {
            std::this_thread::sleep_for(std::chrono::seconds(2));
        }
    }).detach();

    ioContext.run();
    return 0;
}
