#include "control.grpc.pb.h"
#include <format>
#include <grpcpp/grpcpp.h>
#include <iostream>

using namespace control;

int main(int argc, char **argv)
{
    std::string address = "************:3000";
    auto channel = grpc::CreateChannel(address, grpc::InsecureChannelCredentials());
    auto stub = RadarControl::NewStub(channel);

    grpc::ClientContext context{};
    BaseConfigRequest request{};
    BaseReply reply{};

    request.set_mode(BaseConfigRequest::TrackAndSearch);
    grpc::Status status = stub->SetBaseConfig(&context, request, &reply);
    if (status.ok()) {
        std::cout << "SetBaseConfig RPC succeeded\n";
    } else {
        std::cerr << "SetBaseConfig RPC failed: " << status.error_message() << "\n";
    }
    return 0;
}
