#include <chrono>
#include <format>
#include <functional>
#include <iostream>
#include <set>
#include <thread>
#include <span>

#include "logger.hpp"
#include "tcp_client.hpp"
#include "telegram.hpp"

int main(int argc, char *argv[])
{
    auto ioContext = std::make_shared<asio::io_context>();
    TcpClient<false> client{ioContext};
    auto errFlag{false};

    std::string addr{"127.0.0.1"};
    std::string port{"3000"};
    auto result = client.connect(addr, port);
    if (!result.has_value()) {
        Logger::Get().error(std::format("Connect [{}:{}] error: {}", addr, port, result.error()));
        return -1;
    }

    // 发送数据的线程
    std::thread([&client, &errFlag]() {
        while (true) {
            std::this_thread::sleep_for(std::chrono::seconds(2));
            std::string str = "hello world!";
            std::vector<uint8_t> msg(str.begin(), str.end());
            auto result = client.write(msg);
            if (!result.has_value()) {
                Logger::Get().error(std::format("Write error: {}", result.error()));
                errFlag = true;
                break;
            }
            auto bytes_written = result.value();
            if (bytes_written != msg.size()) {
                Logger::Get().error("Write error: bytes_written != msg.size()");
                errFlag = true;
                break;
            }
        }
    }).detach();

    std::thread([&client, &errFlag]() {
        while (true) {
            try {
                auto header_result = client.read(sizeof(TelegramHeader));
                if (!header_result.has_value()) {
                    Logger::Get().error(std::format("Read error: {}", header_result.error()));
                    errFlag = true;
                    break;
                }
                auto data = header_result.value();
                auto buf = std::span(data);
                auto header = Deserializer<TelegramHeader, std::endian::big>::deserialize(buf);
                auto size = header.Length;

                auto body_result = client.read(size);
                if (!body_result.has_value()) {
                    Logger::Get().error(std::format("Read error: {}", body_result.error()));
                    errFlag = true;
                    break;
                }

                data = body_result.value();
                if (!data.empty()) {
                    std::string received(data.begin(), data.end());
                    Logger::Get().info("Received: {}", received);
                }

                // auto cmd = Deserializer<ReportTargetCmd, std::endian::big>::deserialize(data.data());
                // Logger::Get().info("recieved {} targets", cmd.Targets.size());

            } catch (const std::exception &e) {
                errFlag = true;
                Logger::Get().error("Read error: {}", e.what());
                break;
            }
        }
    }).detach();

    ioContext->run();

    while (!errFlag) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    client.close();
    std::cout << "Client closed." << std::endl;

    return 0;
}
