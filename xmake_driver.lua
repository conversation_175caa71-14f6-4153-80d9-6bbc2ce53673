set_languages("c++latest")

set_plat("cross")
set_arch("arm64")
set_values("linux.driver.linux-headers", "/home/<USER>/petalinux/kernel-source/include")

toolchain("petalinux-toolchains")
    set_kind("standalone")
    add_cxflags("-mcpu=cortex-a72.cortex-a53+crc -mbranch-protection=standard")
    set_sdkdir("/home/<USER>/petalinux/sdk/sysroots/cortexa72-cortexa53-amd-linux")
    set_toolset("cc", "aarch64-linux-gnu-gcc")
    set_toolset("cxx", "aarch64-linux-gnu-g++")
    set_toolset("ld", "aarch64-linux-gnu-g++")
    set_toolset("ar", "aarch64-linux-gnu-ar")
    set_toolset("as", "aarch64-linux-gnu-as")
toolchain_end()

target("comm_driver")
    set_toolchains("petalinux-toolchains")
    add_rules("platform.linux.module")
    add_files("cmd/driver/*.c")
    add_includedirs("/home/<USER>/petalinux/kernel-source/include")
    add_includedirs("/home/<USER>/petalinux/kernel-source/arch/arm64/include")
    add_includedirs("/home/<USER>/petalinux/kernel-source/arch/arm64/include/generated")
    add_includedirs("/home/<USER>/petalinux/kernel-source/arch/arm64/include/uapi")
    add_includedirs("/home/<USER>/petalinux/kernel-source/arch/arm64/include/generated/uapi")
    add_includedirs("/home/<USER>/petalinux/kernel-source/include/uapi")
    add_includedirs("/home/<USER>/petalinux/kernel-source/tools/include/generated")
    add_includedirs("/home/<USER>/petalinux/kernel-source/tools/include/generated/uapi")
 
    -- set_values("linux.driver.linux-headers", "/home/<USER>/petalinux/kernel-source/include")
