add_rules("mode.release", "mode.debug")
set_languages("c++latest")

if is_mode("debug") then
    -- add_ldflags("-static")
    add_cxflags("-fsanitize=address,undefined,leak")
    add_ldflags("-fsanitize=address,undefined,leak")
    add_cxflags("-fno-omit-frame-pointer")
    add_cxflags("-fstack-protector-all")
else 
    add_ldflags("-static")
end

toolchain("arm64-linux-gnu")
    set_kind("standalone")
    add_cxflags("-march=armv8-a")
    add_ldflags("-Wl,--as-needed")
    set_sdkdir("/home/<USER>/toolchain/arm-gnu-toolchain-14.2.rel1-x86_64-aarch64-none-linux-gnu")
toolchain_end()

-- toolchain("arm64-linux-gnu")
--     set_toolset("cc", "aarch64-linux-gnu-gcc")
--     set_toolset("cxx", "aarch64-linux-gnu-g++")
--     set_toolset("ld", "aarch64-linux-gnu-g++")
--     set_toolset("ar", "aarch64-linux-gnu-ar")
--     set_toolset("as", "aarch64-linux-gnu-as")
--     set_toolset("strip", "aarch64-linux-gnu-strip")
-- toolchain_end()
-- set_toolchains("arm64-linux-gnu")

if is_plat("cross") then
    set_toolchains("arm64-linux-gnu")
end


add_requires("grpc", {system = false, configs = {shared = false, static = true}})
add_requires("protobuf-cpp", {system = false, configs = {shared = false, static = true}})
add_requires("yaml-cpp", {system = false, configs = {shared = false, static = true}})
add_requires("taywee_args")
add_requires("spdlog")
add_requires("asio")
add_requires("gtest", {system = false, configs = {shared = false, static = true}})
add_requires("benchmark", {system = false, configs = {shared = false, static = true}})



-- 判断是否为 cross 平台
if is_plat("cross") then
    print("${yellow}当前为 cross 平台构建, controller build")
    set_toolchains("arm64-linux-gnu")
else 
    print("${yellow}当前为 %s 平台构建, proto build", os.host())
    target("proto")
        set_kind("phony")
        add_rules("protobuf.cpp")
        add_packages("grpc", "protobuf-cpp")
        add_files("proto/control.proto", {proto_rootdir = "proto", proto_grpc_cpp_plugin = true})
        after_build(function (target)
            -- 复制文件到目标目录
            os.cp("build/.gens/proto/linux/x86_64/release/rules/protobuf/proto/*", "proto/")
        end)
end



target("controller")
    add_packages("grpc", "protobuf-cpp", "yaml-cpp", "taywee_args", "spdlog")
    add_includedirs("modules")
    add_files("cmd/controller/main.cpp")
    add_files("proto/*.cc")
    add_includedirs("proto")
    after_build(function (target)
        os.mkdir("stage")
        os.cp(path.join(target:targetdir(), "controller"), "stage/")
        os.cp("config/controller.yaml", "stage/")
        os.cp("config/controller.yaml", target:targetdir())
    end)

target("signal")
    add_packages("grpc", "yaml-cpp", "taywee_args", "spdlog", "asio")
    add_includedirs("modules")
    add_files("cmd/signal/main.cpp")
    after_build(function (target)
        os.mkdir("stage")
        os.cp(path.join(target:targetdir(), "signal"), "stage/")
        os.cp("config/signal.yaml", "stage/")
        os.cp("config/signal.yaml", target:targetdir())
    end)


target("test_grpc_client")
    add_packages("grpc", "protobuf-cpp")
    add_includedirs("modules")
    add_files("test/grpc_client/main.cpp")
    add_files("proto/*.cc")
    add_includedirs("proto")
    after_build(function (target)
        os.mkdir("stage")
        os.cp(path.join(target:targetdir(), "test_grpc_client"), "stage/")
    end)

target("test_tcp_client")
    add_packages("asio", "spdlog")
    add_includedirs("modules")
    add_files("test/tcp_client/*.cpp")
    after_build(function (target)
        os.mkdir("stage")
        os.cp(path.join(target:targetdir(), "test_tcp_client"), "stage/")
    end)


target("test_tcp_server")
    add_packages("asio", "spdlog")
    add_includedirs("modules")
    add_files("test/tcp_server/*.cpp")
    after_build(function (target)
        os.mkdir("stage")
        os.cp(path.join(target:targetdir(), "test_tcp_server"), "stage/")
    end)