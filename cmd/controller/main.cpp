#include <format>
#include <iostream>

#include "app_args.hpp"
#include "app_config.hpp"
#include "server.hpp"

auto main(int argc, char **argv) -> int
{
    auto [ok, arguments] = ParseArgs(argc, argv);
    if (!ok) {
        return -1;
    }

    if (!arguments.workDir.empty()) {
        std::filesystem::current_path(arguments.workDir);
    } else {
        std::filesystem::current_path(std::filesystem::path(argv[0]).parent_path());
    }

    std::cout << "work dir is " << std::filesystem::current_path() << std::endl;
    auto result = LoadConfig("./controller.yaml");
    if (!result) {
        std::cerr << "Failed to load config: " << result.error() << std::endl;
        return -1;
    }

    auto conf = result.value();
    Logger::Get().Start(conf.log, "controller");
    Logger::Get().info("controller started with work dir: {}",
                        std::filesystem::current_path().string());
    Logger::Get().info("controller read config from ./config.yaml");
    Logger::Get().info(std::format("Loaded config: {}", conf));
    Logger::Get().info("[{}] is running!", "radar controller");
    RunServer();
    Logger::Get().info("[{}] has been closed!", "radar controller");
    return 0;
}