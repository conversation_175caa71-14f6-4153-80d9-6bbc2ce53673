#pragma once

#include <args.hxx>

struct Arguments {
    std::string workDir;
};

auto ParseArgs(int argc, char **argv)
{
    args::ArgumentParser parser("echo radar controller program.",
                                "This goes after the options.");
    args::HelpFlag help(parser, "help", "Display this help menu", {'h', "help"});
    args::ValueFlag<std::string> workDirArg(parser, "workdir", "Set working directory",
                                            {'w', "workDir"}, "");
    try {
        parser.ParseCLI(argc, argv);
    } catch (args::Help) {
        std::cout << parser;
        return std::tuple<bool, Arguments>{false, Arguments{}};
    } catch (args::ParseError e) {
        std::cerr << e.what() << std::endl;
        std::cerr << parser;
        return std::tuple<bool, Arguments>{false, Arguments{}};
    } catch (args::ValidationError e) {
        std::cerr << e.what() << std::endl;
        std::cerr << parser;
        return std::tuple<bool, Arguments>{false, Arguments{}};
    }

    std::string workDir = args::get(workDirArg);
    return std::tuple<bool, Arguments>{true, Arguments{workDir}};
}