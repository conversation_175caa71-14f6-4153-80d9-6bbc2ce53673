#pragma once
#include "control.grpc.pb.h"

#include <format>
#include <grpc/grpc.h>
#include <grpcpp/security/server_credentials.h>
#include <grpcpp/server.h>
#include <grpcpp/server_builder.h>
#include <grpcpp/server_context.h>

using grpc::Server;
using grpc::ServerBuilder;
using grpc::ServerContext;
using grpc::Status;

using namespace control;

template <>
struct std::formatter<BaseConfigRequest_Mode, char> : std::formatter<std::string, char> {
    template <typename FormatContext>
    auto format(const BaseConfigRequest_Mode &item, FormatContext &ctx) const
    {
        std::string ret;
        switch (item) {
        case BaseConfigRequest_Mode::BaseConfigRequest_Mode_TrackWhileSearch:
            ret = "Track While Search";
            break;
        case BaseConfigRequest_Mode::BaseConfigRequest_Mode_TrackAndSearch:
            ret = "Track And Search";
            break;
        default:
            ret = "Unknown Mode";
            break;
        }
        return std::formatter<std::string, char>::format(ret, ctx);
    }
};

template <typename T> class ControlServiceImpl final : public RadarControl::Service
{
  public:
    explicit ControlServiceImpl() {}

    grpc::Status SetBaseConfig(grpc::ServerContext *context, const BaseConfigRequest *request,
                               BaseReply *response) override
    {
        std::cout << std::format("request SetBaseConfig: mode={}", request->mode()) << std::endl;
        if (request->mode() == BaseConfigRequest::TrackWhileSearch) {
            response->set_code(1);
            return grpc::Status::OK;
        } else {
            response->set_code(0);
            return grpc::Status::OK;
        }
    }

  private:
};

void RunServer()
{
    std::string server_address("0.0.0.0:3000");
    ControlServiceImpl<int> service{};

    ServerBuilder builder;
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    builder.RegisterService(&service);
    std::unique_ptr<Server> server(builder.BuildAndStart());
    if (server == nullptr) {
        std::cout << "Server failed to start.\n";
        return;
    }
    std::cout << "Server listening on " << server_address << std::endl;
    server->Wait();
}