#pragma once

#include <expected>
#include <tuple>

#include "config.hpp"
#include "logger.hpp"

template <bool ph> struct GRPCConfigTpl {
    int port;
    std::string host;
};

typedef GRPCConfigTpl<true> GRPCConfig;

template <bool ph> struct AppConfigTpl {
    GRPCConfig grpc;
    LogConfig log;
};
typedef AppConfigTpl<true> AppConfig;

YAML_COVERTER(GRPCConfig, port, host);
YAML_COVERTER(LogConfig, level, path, maxSize, maxFiles, console);
YAML_COVERTER(AppConfig, grpc, log);

std::expected<AppConfig, std::string> LoadConfig(std::string path)
{
    if (!std::filesystem::exists(path)) {
        return std::unexpected("Config file does not exist: " + path);
    }
    YAML::Node node = YAML::LoadFile(path);
    auto config = node.as<AppConfig>();
    return config;
}

template <> struct std::formatter<GRPCConfig, char> : std::formatter<std::string, char> {
    template <typename FormatContext> auto format(const GRPCConfig &grpc, FormatContext &ctx) const
    {
        std::ostringstream oss;
        oss << "{host: " << grpc.host << ", port: " << grpc.port << "}";
        return std::formatter<std::string, char>::format(oss.str(), ctx);
    }
};

template <> struct std::formatter<AppConfig, char> : std::formatter<std::string, char> {
    template <typename FormatContext> auto format(const AppConfig &cfg, FormatContext &ctx) const
    {
        std::ostringstream oss;
        oss << "{grpc: " << std::format("{}", cfg.grpc) << ", log: " << std::format("{}", cfg.log)
            << "}";
        return std::formatter<std::string, char>::format(oss.str(), ctx);
    }
};