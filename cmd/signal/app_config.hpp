#pragma once

#include <expected>
#include <tuple>

#include "config.hpp"
#include "logger.hpp"

template <bool ph> struct TCPConfigTpl {
    std::string host;
    int port;
};

typedef TCPConfigTpl<true> TCPConfig;

template <bool ph> struct AppConfigTpl {
    TCPConfigTpl<ph> server;
    LogConfig log;
};
typedef AppConfigTpl<true> AppConfig;

YAML_COVERTER(TCPConfig, port, host);
YAML_COVERTER(LogConfig, level, path, maxSize, maxFiles, console);
YAML_COVERTER(AppConfig, server, log);

std::expected<AppConfig, std::string> LoadConfig(std::string path)
{
    if (!std::filesystem::exists(path)) {
        return std::unexpected("Config file does not exist: " + path);
    }
    YAML::Node node = YAML::LoadFile(path);
    auto config = node.as<AppConfig>();
    return config;
}

template <> struct std::formatter<TCPConfig, char> : std::formatter<std::string, char> {
    template <typename FormatContext> auto format(const TCPConfig &tcp, FormatContext &ctx) const
    {
        std::ostringstream oss;
        oss << "{host: " << tcp.host << ", port: " << tcp.port << "}";
        return std::formatter<std::string, char>::format(oss.str(), ctx);
    }
};


template <> struct std::formatter<AppConfig, char> : std::formatter<std::string, char> {
    template <typename FormatContext> auto format(const AppConfig &cfg, FormatContext &ctx) const
    {
        std::ostringstream oss;
        oss << "{server: " << std::format("{}", cfg.server)
            << ", log: " << std::format("{}", cfg.log) << "}";
        return std::formatter<std::string, char>::format(oss.str(), ctx);
    }
};