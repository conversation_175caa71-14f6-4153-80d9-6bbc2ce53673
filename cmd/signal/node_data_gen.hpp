#pragma once

#include <array>
#include <chrono>
#include <list>
#include <memory>
#include <queue>
#include <thread>
#include <vector>

#include <asio.hpp>

#include "logger.hpp"
#include "node_base.hpp"
#include "tcp_server.hpp"
#include "telegram.hpp"
#include "thread_utils.hpp"

template <typename Queue> class NodeDataGen : public DataFlow::ProcessNode<Queue>
{
  private:
    using Base = DataFlow::ProcessNode<Queue>;

  public:
    NodeDataGen()
    {
        Base::_outputQueues.resize(1);
    }
    ~NodeDataGen() = default;

    int Start()
    {
        thread_ = std::thread(&NodeDataGen::_loop, this);
        SetThreadName(thread_, "NdDataGen");

        return 0;
    }

    void Join()
    {
        thread_.join();
    }

    std::map<std::string, std::size_t> Status()
    {
        std::size_t sendSuccess = sendSuccess_;
        std::size_t sendFail = sendFail_;
        sendSuccess_ = 0;
        sendFail_ = 0;

        return std::map<std::string, std::size_t>{{"datagen.success", sendSuccess},
                                                  {"datagen.fail", sendFail}};
    }

  private:
    void _loop()
    {
        while (true) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            std::vector<std::shared_ptr<std::vector<uint8_t>>> data;
            auto buf = std::make_shared<std::vector<std::uint8_t>>(sizeof(TelegramHeader));
            TelegramHeader header{.Magic = 0x1234,
                                  .Command = 0x0001,
                                  .Length = sizeof(TelegramHeader),
                                  .Reverse1 = 0x0000};
            ReportTargetCmd cmd{.Info{.FrameSeq = 1,
                                      .CPINum = 1,
                                      .AzimuthBegin = 1,
                                      .ElevationBegin = 1,
                                      .TargetNum = 1},
                                .Targets{
                                    {.Azimuth = 1,
                                     .Elevation = 1,
                                     .Range = 1,
                                     .Doppler = 1,
                                     .Ampplitude = 1,
                                     .SNR = 1},
                                }};
            auto size = sizeof(ReportTargetInfo) + cmd.Targets.size() * sizeof(ReportTarget);
            header.Length = size;
            auto span = std::span<std::uint8_t>(*buf);
            Serializer<TelegramHeader, std::endian::big>::serialize(header, span);
            data.push_back(buf);

            buf = std::make_shared<std::vector<std::uint8_t>>(size);
            span = std::span<std::uint8_t>(*buf);
            Serializer<ReportTargetCmd, std::endian::big>::serialize(cmd, span);
            data.push_back(buf);

            for (auto &buf : data) {
                auto [ok, flag] = Base::_outputQueues[0]->push(buf);
                if (!ok) {
                    sendFail_++;
                    if (flag) {
                        break;
                    }
                    continue;
                }
            }
        }
    }

    std::thread thread_;
    std::size_t sendSuccess_{0};
    std::size_t sendFail_{0};
};
