#pragma once

#include <array>
#include <chrono>
#include <functional>
#include <list>
#include <memory>
#include <queue>
#include <string>
#include <thread>
#include <vector>

#include <asio.hpp>

#include "logger.hpp"
#include "node_base.hpp"
#include "tcp_server.hpp"
#include "thread_utils.hpp"

template <typename Queue> class NodeTCPSender : public DataFlow::ProcessNode<Queue>
{
  private:
    constexpr static int cacheSize = 65535;
    using Base = DataFlow::ProcessNode<Queue>;
    using ServType = TCPServer<1, cacheSize>;

  public:
    NodeTCPSender(std::string addr, std::uint16_t port) : addr_(addr), port_(port)
    {
        Base::_inputQueues.resize(1);
    }
    ~NodeTCPSender() = default;

    int Start()
    {
        auto ignoreSize = 1024;
        auto waitTime = std::chrono::milliseconds(100);

        auto tryNextWrite = std::make_shared<std::function<void(ServType::SSPtr)>>();
        *tryNextWrite = [this, waitTime, tryNextWrite](ServType::SSPtr s) {
            auto [ptr, exitFlag] = Base::_inputQueues[0]->template pop<std::vector<uint8_t>>();
            if (exitFlag) {
                s->stop();
                ioContext_.stop();
                return;
            }
            if (ptr == nullptr) {
                auto timer = std::make_shared<asio::steady_timer>(ioContext_, waitTime);
                timer->async_wait([this, s, timer, tryNextWrite](const asio::error_code &) { (*tryNextWrite)(s); });
                return;
            }
            s->write(*ptr);
        };

        server_ = std::make_shared<ServType>(
            ioContext_, port_,
            [tryNextWrite, ignoreSize](ServType::SSPtr s) {
                Logger::Get().info("client connected");
                s->read(ignoreSize);
                (*tryNextWrite)(s);
            },
            [](ServType::SSPtr s) { Logger::Get().info("client disconnected"); },
            [ignoreSize](ServType::SSPtr s, std::vector<uint8_t> &&data) {
                Logger::Get().info("client red");
                s->read(ignoreSize);
            },
            [tryNextWrite](ServType::SSPtr s) {
                Logger::Get().info("client wrote");
                (*tryNextWrite)(s);
            });


        thread_ = std::thread([&]() { ioContext_.run(); });
        SetThreadName(thread_, "NdTCPIOCtx");

        return 0;
    }

    void Join()
    {
        thread_.join();
    }

    std::map<std::string, std::size_t> Status()
    {
        std::size_t sendSuccess = sendSuccess_;
        std::size_t sendFail = sendFail_;
        sendSuccess_ = 0;
        sendFail_ = 0;

        return std::map<std::string, std::size_t>{{"sender.success", sendSuccess},
                                                  {"sender.fail", sendFail}};
    }

    asio::io_context ioContext_;
    std::shared_ptr<ServType> server_;
    std::size_t sendSuccess_{0};
    std::size_t sendFail_{0};
    std::thread thread_;
    std::string addr_;
    std::uint16_t port_;
};
