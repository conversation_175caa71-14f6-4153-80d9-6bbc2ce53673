#include <bit>
#include <format>
#include <iostream>

#include "app_args.hpp"
#include "app_config.hpp"
#include "framework.hpp"
#include "locked_queue.hpp"
#include "node_data_gen.hpp"
#include "node_tcp_sender.hpp"
#include "telegram.hpp"

typedef DataFlow::LockedQueueNoWait Queue;
constexpr int recvBatch = 4;
constexpr int packageBatch = 1;
constexpr int queueSize = 100;

auto main(int argc, char **argv) -> int
{
    auto [ok, arguments] = ParseArgs(argc, argv);
    if (!ok) {
        return -1;
    }

    if (!arguments.workDir.empty()) {
        std::filesystem::current_path(arguments.workDir);
    } else {
        std::filesystem::current_path(std::filesystem::path(argv[0]).parent_path());
    }

    Logger::Get().info("work dir is {}", std::filesystem::current_path().string());
    std::string configFileName{"./signal.yaml"};
    Logger::Get().info("read config from {}", configFileName);
    auto result = LoadConfig(configFileName);
    if (!result) {
        Logger::Get().error("Failed to load config: {}", result.error());
        return -1;
    }
    auto config = result.value();

    Logger::Get().Start(config.log, "signal");
    Logger::Get().info("signal processor started with work dir: {}",
                       std::filesystem::current_path().string());
    Logger::Get().info(std::format("Loaded config: {}", config));
    Logger::Get().info("[{}] is running!", "signal processor");

    auto source = std::make_shared<NodeDataGen<Queue>>();
    auto tcpSender = std::make_shared<NodeTCPSender<Queue>>(config.server.host, config.server.port);

    DataFlow::Framework<Queue> framework;
    framework.Connect(source, tcpSender, 0, 0, "source->tcpsender", queueSize);

    framework.Start();
    framework.Join();

    Logger::Get().info("[{}] has been closed!", "signal processor");
    return 0;
}