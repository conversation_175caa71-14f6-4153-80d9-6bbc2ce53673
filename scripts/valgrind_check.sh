#!/bin/bash

# Valgrind 内存检测脚本
# 用法: ./scripts/valgrind_check.sh <target_name> [args...]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查参数
if [ $# -lt 1 ]; then
    echo -e "${RED}错误: 请提供目标程序名称${NC}"
    echo "用法: $0 <target_name> [args...]"
    echo "示例: $0 controller"
    echo "示例: $0 test_grpc_client --help"
    exit 1
fi

TARGET_NAME=$1
shift  # 移除第一个参数，剩下的作为程序参数

# 查找可执行文件
EXECUTABLE=""
if [ -f "build/linux/x86_64/debug/${TARGET_NAME}" ]; then
    EXECUTABLE="build/linux/x86_64/debug/${TARGET_NAME}"
elif [ -f "stage/${TARGET_NAME}" ]; then
    EXECUTABLE="stage/${TARGET_NAME}"
else
    echo -e "${RED}错误: 找不到可执行文件 ${TARGET_NAME}${NC}"
    echo "请先编译项目: xmake build ${TARGET_NAME}"
    exit 1
fi

echo -e "${GREEN}找到可执行文件: ${EXECUTABLE}${NC}"

# 检查 Valgrind 是否安装
if ! command -v valgrind &> /dev/null; then
    echo -e "${RED}错误: Valgrind 未安装${NC}"
    echo "请安装 Valgrind:"
    echo "  Ubuntu/Debian: sudo apt-get install valgrind"
    echo "  CentOS/RHEL: sudo yum install valgrind"
    echo "  Arch: sudo pacman -S valgrind"
    exit 1
fi

# 创建日志目录
mkdir -p logs/valgrind

# Valgrind 选项
VALGRIND_OPTS=(
    "--tool=memcheck"
    "--leak-check=full"
    "--show-leak-kinds=all"
    "--track-origins=yes"
    "--verbose"
    "--log-file=logs/valgrind/${TARGET_NAME}_$(date +%Y%m%d_%H%M%S).log"
    "--error-exitcode=1"
    "--suppressions=/usr/share/valgrind/default.supp"
)

echo -e "${YELLOW}开始 Valgrind 内存检测...${NC}"
echo "目标程序: ${EXECUTABLE}"
echo "程序参数: $@"
echo "日志文件: logs/valgrind/${TARGET_NAME}_$(date +%Y%m%d_%H%M%S).log"
echo ""

# 运行 Valgrind
if valgrind "${VALGRIND_OPTS[@]}" "${EXECUTABLE}" "$@"; then
    echo -e "${GREEN}✓ 内存检测通过，未发现内存泄露或越界访问${NC}"
else
    echo -e "${RED}✗ 发现内存问题，请查看日志文件${NC}"
    echo "最新日志文件: $(ls -t logs/valgrind/${TARGET_NAME}_*.log | head -1)"
    exit 1
fi
