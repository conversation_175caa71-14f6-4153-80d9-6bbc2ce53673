[{"directory": "/home/<USER>/code/radar", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-g", "-O0", "-std=c++26", "-<PERSON><PERSON><PERSON><PERSON>", "-DCARES_STATICLIB", "-isystem", "/home/<USER>/.xmake/packages/g/grpc/v1.69.0/ba60b3ca0dc04dae9ee7d17ca1a6729a/include", "-isystem", "/home/<USER>/.xmake/packages/c/c-ares/1.34.5/39e6c0c8c5944e8cbeec0ccc34ba5da2/include", "-isystem", "/home/<USER>/.xmake/packages/r/re2/2024.07.02/fb32d923b9f041eb947d53204a6db66d/include", "-isystem", "/home/<USER>/.xmake/packages/p/protobuf-cpp/31.1/95049c9d3b07455cb326eea40c596545/include", "-isystem", "/home/<USER>/.xmake/packages/a/abseil/20250512.1/e4c0dc1e9e7045ce9e992520836da47e/include", "-isystem", "/home/<USER>/.xmake/packages/o/openssl3/3.3.2/bf71fe061d9c4d32992499a7f1f307c9/include", "-isystem", "/home/<USER>/.xmake/packages/y/yaml-cpp/0.8.0/c41b70635ed74ae9a0628bd794bd504b/include", "-isystem", "/home/<USER>/.xmake/packages/t/taywee_args/6.4.7/a6df59fcbc7b4d0ca1ee4e0ef7e79e83/include", "-isystem", "/home/<USER>/.xmake/packages/s/spdlog/v1.15.3/281ab689e3ca4a04813dc400d53c7ac7/include", "-isystem", "/home/<USER>/.xmake/packages/a/asio/1.34.2/08f9e97208304bfe9403bd2884513fa1/include", "-fsanitize=address,undefined,leak", "-fno-omit-frame-pointer", "-fstack-protector-all", "-o", "build/.objs/signal/linux/x86_64/debug/cmd/signal/main.cpp.o", "cmd/signal/main.cpp"], "file": "cmd/signal/main.cpp"}, {"directory": "/home/<USER>/code/radar", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-g", "-O0", "-std=c++26", "-<PERSON><PERSON><PERSON><PERSON>", "-isystem", "/home/<USER>/.xmake/packages/a/asio/1.34.2/08f9e97208304bfe9403bd2884513fa1/include", "-isystem", "/home/<USER>/.xmake/packages/s/spdlog/v1.15.3/281ab689e3ca4a04813dc400d53c7ac7/include", "-fsanitize=address,undefined,leak", "-fno-omit-frame-pointer", "-fstack-protector-all", "-o", "build/.objs/test_tcp_server/linux/x86_64/debug/test/tcp_server/main.cpp.o", "test/tcp_server/main.cpp"], "file": "test/tcp_server/main.cpp"}, {"directory": "/home/<USER>/code/radar", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-g", "-O0", "-std=c++26", "-<PERSON><PERSON><PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>", "-DCARES_STATICLIB", "-isystem", "/home/<USER>/.xmake/packages/g/grpc/v1.69.0/ba60b3ca0dc04dae9ee7d17ca1a6729a/include", "-isystem", "/home/<USER>/.xmake/packages/c/c-ares/1.34.5/39e6c0c8c5944e8cbeec0ccc34ba5da2/include", "-isystem", "/home/<USER>/.xmake/packages/r/re2/2024.07.02/fb32d923b9f041eb947d53204a6db66d/include", "-isystem", "/home/<USER>/.xmake/packages/o/openssl3/3.3.2/bf71fe061d9c4d32992499a7f1f307c9/include", "-isystem", "/home/<USER>/.xmake/packages/p/protobuf-cpp/31.1/95049c9d3b07455cb326eea40c596545/include", "-isystem", "/home/<USER>/.xmake/packages/a/abseil/20250512.1/e4c0dc1e9e7045ce9e992520836da47e/include", "-fsanitize=address,undefined,leak", "-fno-omit-frame-pointer", "-fstack-protector-all", "-o", "build/.objs/test_grpc_client/linux/x86_64/debug/test/grpc_client/main.cpp.o", "test/grpc_client/main.cpp"], "file": "test/grpc_client/main.cpp"}, {"directory": "/home/<USER>/code/radar", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-g", "-O0", "-std=c++26", "-<PERSON><PERSON><PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>", "-DCARES_STATICLIB", "-isystem", "/home/<USER>/.xmake/packages/g/grpc/v1.69.0/ba60b3ca0dc04dae9ee7d17ca1a6729a/include", "-isystem", "/home/<USER>/.xmake/packages/c/c-ares/1.34.5/39e6c0c8c5944e8cbeec0ccc34ba5da2/include", "-isystem", "/home/<USER>/.xmake/packages/r/re2/2024.07.02/fb32d923b9f041eb947d53204a6db66d/include", "-isystem", "/home/<USER>/.xmake/packages/o/openssl3/3.3.2/bf71fe061d9c4d32992499a7f1f307c9/include", "-isystem", "/home/<USER>/.xmake/packages/p/protobuf-cpp/31.1/95049c9d3b07455cb326eea40c596545/include", "-isystem", "/home/<USER>/.xmake/packages/a/abseil/20250512.1/e4c0dc1e9e7045ce9e992520836da47e/include", "-fsanitize=address,undefined,leak", "-fno-omit-frame-pointer", "-fstack-protector-all", "-o", "build/.objs/test_grpc_client/linux/x86_64/debug/proto/control.grpc.pb.cc.o", "proto/control.grpc.pb.cc"], "file": "proto/control.grpc.pb.cc"}, {"directory": "/home/<USER>/code/radar", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-g", "-O0", "-std=c++26", "-<PERSON><PERSON><PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>", "-DCARES_STATICLIB", "-isystem", "/home/<USER>/.xmake/packages/g/grpc/v1.69.0/ba60b3ca0dc04dae9ee7d17ca1a6729a/include", "-isystem", "/home/<USER>/.xmake/packages/c/c-ares/1.34.5/39e6c0c8c5944e8cbeec0ccc34ba5da2/include", "-isystem", "/home/<USER>/.xmake/packages/r/re2/2024.07.02/fb32d923b9f041eb947d53204a6db66d/include", "-isystem", "/home/<USER>/.xmake/packages/o/openssl3/3.3.2/bf71fe061d9c4d32992499a7f1f307c9/include", "-isystem", "/home/<USER>/.xmake/packages/p/protobuf-cpp/31.1/95049c9d3b07455cb326eea40c596545/include", "-isystem", "/home/<USER>/.xmake/packages/a/abseil/20250512.1/e4c0dc1e9e7045ce9e992520836da47e/include", "-fsanitize=address,undefined,leak", "-fno-omit-frame-pointer", "-fstack-protector-all", "-o", "build/.objs/test_grpc_client/linux/x86_64/debug/proto/control.pb.cc.o", "proto/control.pb.cc"], "file": "proto/control.pb.cc"}, {"directory": "/home/<USER>/code/radar", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-g", "-O0", "-std=c++26", "-<PERSON><PERSON><PERSON><PERSON>", "-isystem", "/home/<USER>/.xmake/packages/a/asio/1.34.2/08f9e97208304bfe9403bd2884513fa1/include", "-isystem", "/home/<USER>/.xmake/packages/s/spdlog/v1.15.3/281ab689e3ca4a04813dc400d53c7ac7/include", "-fsanitize=address,undefined,leak", "-fno-omit-frame-pointer", "-fstack-protector-all", "-o", "build/.objs/test_tcp_client/linux/x86_64/debug/test/tcp_client/main.cpp.o", "test/tcp_client/main.cpp"], "file": "test/tcp_client/main.cpp"}, {"directory": "/home/<USER>/code/radar", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-g", "-O0", "-std=c++26", "-<PERSON><PERSON><PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>", "-DCARES_STATICLIB", "-isystem", "/home/<USER>/.xmake/packages/g/grpc/v1.69.0/ba60b3ca0dc04dae9ee7d17ca1a6729a/include", "-isystem", "/home/<USER>/.xmake/packages/c/c-ares/1.34.5/39e6c0c8c5944e8cbeec0ccc34ba5da2/include", "-isystem", "/home/<USER>/.xmake/packages/r/re2/2024.07.02/fb32d923b9f041eb947d53204a6db66d/include", "-isystem", "/home/<USER>/.xmake/packages/o/openssl3/3.3.2/bf71fe061d9c4d32992499a7f1f307c9/include", "-isystem", "/home/<USER>/.xmake/packages/p/protobuf-cpp/31.1/95049c9d3b07455cb326eea40c596545/include", "-isystem", "/home/<USER>/.xmake/packages/a/abseil/20250512.1/e4c0dc1e9e7045ce9e992520836da47e/include", "-isystem", "/home/<USER>/.xmake/packages/y/yaml-cpp/0.8.0/c41b70635ed74ae9a0628bd794bd504b/include", "-isystem", "/home/<USER>/.xmake/packages/t/taywee_args/6.4.7/a6df59fcbc7b4d0ca1ee4e0ef7e79e83/include", "-isystem", "/home/<USER>/.xmake/packages/s/spdlog/v1.15.3/281ab689e3ca4a04813dc400d53c7ac7/include", "-fsanitize=address,undefined,leak", "-fno-omit-frame-pointer", "-fstack-protector-all", "-o", "build/.objs/controller/linux/x86_64/debug/cmd/controller/main.cpp.o", "cmd/controller/main.cpp"], "file": "cmd/controller/main.cpp"}, {"directory": "/home/<USER>/code/radar", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-g", "-O0", "-std=c++26", "-<PERSON><PERSON><PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>", "-DCARES_STATICLIB", "-isystem", "/home/<USER>/.xmake/packages/g/grpc/v1.69.0/ba60b3ca0dc04dae9ee7d17ca1a6729a/include", "-isystem", "/home/<USER>/.xmake/packages/c/c-ares/1.34.5/39e6c0c8c5944e8cbeec0ccc34ba5da2/include", "-isystem", "/home/<USER>/.xmake/packages/r/re2/2024.07.02/fb32d923b9f041eb947d53204a6db66d/include", "-isystem", "/home/<USER>/.xmake/packages/o/openssl3/3.3.2/bf71fe061d9c4d32992499a7f1f307c9/include", "-isystem", "/home/<USER>/.xmake/packages/p/protobuf-cpp/31.1/95049c9d3b07455cb326eea40c596545/include", "-isystem", "/home/<USER>/.xmake/packages/a/abseil/20250512.1/e4c0dc1e9e7045ce9e992520836da47e/include", "-isystem", "/home/<USER>/.xmake/packages/y/yaml-cpp/0.8.0/c41b70635ed74ae9a0628bd794bd504b/include", "-isystem", "/home/<USER>/.xmake/packages/t/taywee_args/6.4.7/a6df59fcbc7b4d0ca1ee4e0ef7e79e83/include", "-isystem", "/home/<USER>/.xmake/packages/s/spdlog/v1.15.3/281ab689e3ca4a04813dc400d53c7ac7/include", "-fsanitize=address,undefined,leak", "-fno-omit-frame-pointer", "-fstack-protector-all", "-o", "build/.objs/controller/linux/x86_64/debug/proto/control.grpc.pb.cc.o", "proto/control.grpc.pb.cc"], "file": "proto/control.grpc.pb.cc"}, {"directory": "/home/<USER>/code/radar", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-g", "-O0", "-std=c++26", "-<PERSON><PERSON><PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>", "-DCARES_STATICLIB", "-isystem", "/home/<USER>/.xmake/packages/g/grpc/v1.69.0/ba60b3ca0dc04dae9ee7d17ca1a6729a/include", "-isystem", "/home/<USER>/.xmake/packages/c/c-ares/1.34.5/39e6c0c8c5944e8cbeec0ccc34ba5da2/include", "-isystem", "/home/<USER>/.xmake/packages/r/re2/2024.07.02/fb32d923b9f041eb947d53204a6db66d/include", "-isystem", "/home/<USER>/.xmake/packages/o/openssl3/3.3.2/bf71fe061d9c4d32992499a7f1f307c9/include", "-isystem", "/home/<USER>/.xmake/packages/p/protobuf-cpp/31.1/95049c9d3b07455cb326eea40c596545/include", "-isystem", "/home/<USER>/.xmake/packages/a/abseil/20250512.1/e4c0dc1e9e7045ce9e992520836da47e/include", "-isystem", "/home/<USER>/.xmake/packages/y/yaml-cpp/0.8.0/c41b70635ed74ae9a0628bd794bd504b/include", "-isystem", "/home/<USER>/.xmake/packages/t/taywee_args/6.4.7/a6df59fcbc7b4d0ca1ee4e0ef7e79e83/include", "-isystem", "/home/<USER>/.xmake/packages/s/spdlog/v1.15.3/281ab689e3ca4a04813dc400d53c7ac7/include", "-fsanitize=address,undefined,leak", "-fno-omit-frame-pointer", "-fstack-protector-all", "-o", "build/.objs/controller/linux/x86_64/debug/proto/control.pb.cc.o", "proto/control.pb.cc"], "file": "proto/control.pb.cc"}]