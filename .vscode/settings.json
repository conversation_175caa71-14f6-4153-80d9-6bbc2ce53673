{"files.associations": {"format": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "span": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp", "variant": "cpp", "text_encoding": "cpp", "ranges": "cpp", "bitset": "cpp", "chrono": "cpp", "codecvt": "cpp", "condition_variable": "cpp", "cstring": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "unordered_set": "cpp", "ratio": "cpp", "iomanip": "cpp", "mutex": "cpp", "semaphore": "cpp", "sstream": "cpp", "stop_token": "cpp", "thread": "cpp", "cinttypes": "cpp", "*.inc": "cpp", "any": "cpp", "fstream": "cpp", "shared_mutex": "cpp", "valarray": "cpp", "filesystem": "cpp", "cerrno": "cpp", "expected": "cpp", "coroutine": "cpp", "csignal": "cpp", "source_location": "cpp", "future": "cpp", "regex": "cpp", "cassert": "cpp", "climits": "cpp", "cfloat": "cpp", "version": "cpp"}, "xmake.customDebugConfig": {"miDebuggerPath": "/usr/bin/gdb"}, "xmake.executable": "/home/<USER>/.local/bin/xmake"}