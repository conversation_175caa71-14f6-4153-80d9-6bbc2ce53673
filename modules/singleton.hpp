#pragma once
#include <type_traits>

template <typename Type> class Singleton
{
  public:
    static Type &Get()
    {
        if consteval {
            static_assert(std::is_default_constructible_v<Type>, "Singleton类型必须可默认构造");
        }

        static Type instance;
        return instance;
    }
    Singleton(const Singleton &) = delete;
    Singleton &operator=(const Singleton &) = delete;
    Singleton(Singleton &&) = delete;
    Singleton &operator=(Singleton &&) = delete;

  protected:
    Singleton() = default;
    virtual ~Singleton() = default;
};
