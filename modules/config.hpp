#pragma
#include <string>
#include <yaml-cpp/yaml.h>

// Support at most 4 parameters conversion. The number can be expanded by adding:
// YAML_TO_CLASS_[N](TYPE, NAME, ...) CLASS.NAME = node[#NAME].as<TYPE>();
// YAML_TO_CLASS_[N-1](__VA_ARGS__)

#define YAML_TO_CLASS_1(CLASS, NAME, ...) rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();
#define YAML_TO_CLASS_2(CLASS, NAME, ...)                                                          \
    rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();                                            \
    YAML_TO_CLASS_1(CLASS, __VA_ARGS__)
#define YAML_TO_CLASS_3(CLASS, NAME, ...)                                                          \
    rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();                                            \
    YAML_TO_CLASS_2(CLA<PERSON>, __VA_ARGS__)
#define YAML_TO_CLASS_4(CLASS, NAME, ...)                                                          \
    rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();                                            \
    YAML_TO_CLASS_3(CLASS, __VA_ARGS__)
#define YAML_TO_CLASS_5(CLASS, NAME, ...)                                                          \
    rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();                                            \
    YAML_TO_CLASS_4(CLASS, __VA_ARGS__)

#define CLASS_TO_YAML_1(NAME, ...) node[#NAME] = rhs.NAME;
#define CLASS_TO_YAML_2(NAME, ...)                                                                 \
    node[#NAME] = rhs.NAME;                                                                        \
    CLASS_TO_YAML_1(__VA_ARGS__)
#define CLASS_TO_YAML_3(NAME, ...)                                                                 \
    node[#NAME] = rhs.NAME;                                                                        \
    CLASS_TO_YAML_2(__VA_ARGS__)
#define CLASS_TO_YAML_4(NAME, ...)                                                                 \
    node[#NAME] = rhs.NAME;                                                                        \
    CLASS_TO_YAML_3(__VA_ARGS__)
#define CLASS_TO_YAML_5(NAME, ...)                                                                 \
    node[#NAME] = rhs.NAME;                                                                        \
    CLASS_TO_YAML_4(__VA_ARGS__)

#define NUM_ARGS_IMPL(_1, _2, _3, _4, _5, N, ...) N
#define NUM_ARGS(...) NUM_ARGS_IMPL(__VA_ARGS__, 5, 4, 3, 2, 1)

#define CAT(a, b) CAT_IMPL(a, b)
#define CAT_IMPL(a, b) a##b

#define YAML_COVERTER_N(CLASS, NUM, ...)                                                           \
    namespace YAML                                                                                 \
    {                                                                                              \
    template <> struct convert<CLASS> {                                                            \
        static Node encode(const CLASS &rhs)                                                       \
        {                                                                                          \
            Node node;                                                                             \
            CAT(CLASS_TO_YAML_, NUM)(__VA_ARGS__);                                                 \
            return node;                                                                           \
        }                                                                                          \
                                                                                                   \
        static bool decode(const Node &node, CLASS &rhs)                                           \
        {                                                                                          \
            if (!node.IsMap())                                                                     \
                return false;                                                                      \
            CAT(YAML_TO_CLASS_, NUM)(CLASS, __VA_ARGS__);                                          \
            return true;                                                                           \
        }                                                                                          \
    };                                                                                             \
    }

#define YAML_COVERTER(CLASS, ...) YAML_COVERTER_N(CLASS, NUM_ARGS(__VA_ARGS__), __VA_ARGS__)
