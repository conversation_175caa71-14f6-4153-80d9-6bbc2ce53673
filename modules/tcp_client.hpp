#pragma once
#include <asio.hpp>
#include <expected>
#include <format>
#include <iostream>
#include <string>
#include <system_error>
#include <vector>

enum class TcpError { ConnectError, WriteError, ReadError, SocketError };
template <> struct std::formatter<TcpError> : std::formatter<std::string> {
    auto format(TcpError err, format_context &ctx) const
    {
        std::string error_msg;
        switch (err) {
        case TcpError::ConnectError:
            error_msg = "Failed to connect to server";
            break;
        case TcpError::WriteError:
            error_msg = "Failed to write data to server";
            break;
        case TcpError::ReadError:
            error_msg = "Failed to read data from server";
            break;
        case TcpError::SocketError:
            error_msg = "Socket operation failed";
            break;
        }
        return formatter<string>::format(error_msg, ctx);
    }
};

template <bool IsAsync = false> class TcpClient
{
  public:
    TcpClient(std::shared_ptr<asio::io_context> ioContext)
        : ioContext_(ioContext), socket_(*ioContext)
    {
    }

    // Connect to server (host: IP or hostname, port: string)
    std::expected<void, TcpError> connect(const std::string &host, const std::string &port)
    {
        asio::ip::tcp::resolver resolver(*ioContext_);
        auto endpoints = resolver.resolve(host, port);
        if constexpr (IsAsync) {
            asio::async_connect(socket_, endpoints,
                                [](const asio::error_code &error, const asio::ip::tcp::endpoint &) {
                                    if (error) {
                                        std::cerr << "Connect error: " << error.message()
                                                  << std::endl;
                                    }
                                });
            return {};
        } else {
            try {
                auto endpoint = asio::connect(socket_, endpoints);
                return {};
            } catch (const std::exception &) {
                return std::unexpected(TcpError::ConnectError);
            }
        }
    }

    // Write data to server
    std::expected<size_t, TcpError> write(const std::vector<uint8_t> &data)
    {
        if constexpr (IsAsync) {
            asio::async_write(socket_, asio::buffer(data),
                              [](const asio::error_code &error, std::size_t bytes_transferred) {
                                  if (error) {
                                      std::cerr << "Write error: " << error.message() << std::endl;
                                  }
                              });
            return {data.size()};
        } else {
            try {
                return {asio::write(socket_, asio::buffer(data))};
            } catch (const std::exception &) {
                return std::unexpected(TcpError::WriteError);
            }
        }
    }

    // Read fixed size data from server
    std::expected<std::vector<uint8_t>, TcpError> read(size_t size)
    {
        std::vector<uint8_t> buf(size);
        if constexpr (IsAsync) {
            asio::async_read(socket_, asio::buffer(buf, size),
                             [](const asio::error_code &error, std::size_t bytes_transferred) {
                                 if (error) {
                                     std::cerr << "Read error: " << error.message() << std::endl;
                                 }
                             });
            return std::expected<std::vector<uint8_t>, TcpError>(std::move(buf));
        } else {
            try {
                asio::read(socket_, asio::buffer(buf, size));
                return {std::move(buf)};
            } catch (const std::exception &) {
                return std::unexpected(TcpError::ReadError);
            }
        }
    }

    // Check if there is data available to read
    std::expected<size_t, TcpError> available() const
    {
        asio::error_code ec;
        auto size = socket_.available(ec);
        if (ec) {
            return std::unexpected(TcpError::SocketError);
        }
        return size;
    }

    std::expected<void, TcpError> close()
    {
        if (!socket_.is_open()) {
            return {};
        }

        asio::error_code ec;
        socket_.shutdown(asio::ip::tcp::socket::shutdown_both, ec);
        if (ec) {
            return std::unexpected(TcpError::SocketError);
        }
        socket_.close(ec);
        if (ec) {
            return std::unexpected(TcpError::SocketError);
        }
        return {};
    }

  private:
    std::shared_ptr<asio::io_context> ioContext_;
    asio::ip::tcp::socket socket_;
};