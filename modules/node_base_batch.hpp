#pragma once

#include <array>
#include <chrono>
#include <map>
#include <memory>
#include <thread>
#include <vector>

#include "thread_utils.hpp"

namespace DataFlow {

template <typename T, int ISize, int OSize, typename Queue>
class ProcessNodeBatch : public ProcessNode<Queue> {
  public:
    using Base = ProcessNode<Queue>;
    using Container = std::array<std::shared_ptr<T>, OSize>;

    ProcessNodeBatch()                                   = default;
    ProcessNodeBatch(const ProcessNodeBatch&)            = delete;
    ProcessNodeBatch& operator=(const ProcessNodeBatch&) = delete;
    virtual ~ProcessNodeBatch()                          = default;

    virtual int Start() = 0;
    virtual void Join() = 0;

    bool popFromBatch(std::function<bool(std::shared_ptr<T>)> processFunc) {
        auto [data, exitFlag] =
            Base::_inputQueues[0]
                ->template pop<std::array<std::shared_ptr<T>, ISize>>();
        if (exitFlag) {
            return true;
        }
        if (data == nullptr) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            return false;
        }

        for (std::shared_ptr<T>& element : *data) {
            if (processFunc(element)) {
                return true;
            }
        }
        return false;
    }

    bool pushToBatch(std::shared_ptr<T> element) {
        (*_batch.get())[_index++] = element;
        if (_index == OSize) {
            auto [_, exitFlag] = Base::_outputQueues[0]->push(_batch);
            if (exitFlag) {
                return true;
            }
            _batch = std::make_shared<Container>();
            _index = 0;
        }
        return false;
    }

  protected:
    std::shared_ptr<Container> _batch = std::make_shared<Container>();
    std::size_t _index = 0;
};
} // namespace DataFlow