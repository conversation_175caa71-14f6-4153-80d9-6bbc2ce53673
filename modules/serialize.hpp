#pragma once

#include <bit>
#include <cmath>
#include <cstdint>
#include <span>
#include <vector>

#define SERIALIZE_FIELD(field, buf)                                                        \
    Serializer<decltype(field), DstEndian>::serialize(field, buf);                        \

// 支持1~9个字段的宏，每个宏递归调用前一个宏
#define SERIALIZE_FIELDS_1(obj, f1, buf) SERIALIZE_FIELD(obj.f1, buf);

#define SERIALIZE_FIELDS_2(obj, f1, f2, buf)                                               \
    SERIALIZE_FIELDS_1(obj, f1, buf);                                                      \
    SERIALIZE_FIELD(obj.f2, buf)

#define SERIALIZE_FIELDS_3(obj, f1, f2, f3, buf)                                           \
    SERIALIZE_FIELDS_2(obj, f1, f2, buf);                                                  \
    SERIALIZE_FIELD(obj.f3, buf)

#define SERIALIZE_FIELDS_4(obj, f1, f2, f3, f4, buf)                                       \
    SERIALIZE_FIELDS_3(obj, f1, f2, f3, buf);                                              \
    SERIALIZE_FIELD(obj.f4, buf)

#define SERIALIZE_FIELDS_5(obj, f1, f2, f3, f4, f5, buf)                                   \
    SERIALIZE_FIELDS_4(obj, f1, f2, f3, f4, buf);                                          \
    SERIALIZE_FIELD(obj.f5, buf)

#define SERIALIZE_FIELDS_6(obj, f1, f2, f3, f4, f5, f6, buf)                               \
    SERIALIZE_FIELDS_5(obj, f1, f2, f3, f4, f5, buf);                                      \
    SERIALIZE_FIELD(obj.f6, buf)

#define SERIALIZE_FIELDS_7(obj, f1, f2, f3, f4, f5, f6, f7, buf)                           \
    SERIALIZE_FIELDS_6(obj, f1, f2, f3, f4, f5, f6, buf);                                  \
    SERIALIZE_FIELD(obj.f7, buf)

#define SERIALIZE_FIELDS_8(obj, f1, f2, f3, f4, f5, f6, f7, f8, buf)                       \
    SERIALIZE_FIELDS_7(obj, f1, f2, f3, f4, f5, f6, f7, buf);                              \
    SERIALIZE_FIELD(obj.f8, buf)

#define SERIALIZE_FIELDS_9(obj, f1, f2, f3, f4, f5, f6, f7, f8, f9, buf)                   \
    SERIALIZE_FIELDS_8(obj, f1, f2, f3, f4, f5, f6, f7, f8, buf);                          \
    SERIALIZE_FIELD(obj.f9, buf)

#define SERIALIZE_NUM_ARGS_IMPL(_1, _2, _3, _4, _5, _6, _7, _8, _9, N, ...) N
#define SERIALIZE_NUM_ARGS(...) SERIALIZE_NUM_ARGS_IMPL(__VA_ARGS__, 9, 8, 7, 6, 5, 4, 3, 2, 1)

#define SERIALIZE_FIELDS_SELECT(NAME, NUM) NAME##NUM
#define SERIALIZE_FIELDS_EXPAND(NAME, NUM) SERIALIZE_FIELDS_SELECT(NAME, NUM)
#define SERIALIZE_FIELDS(obj, buf, ...)                                          \
    SERIALIZE_FIELDS_EXPAND(SERIALIZE_FIELDS_, SERIALIZE_NUM_ARGS(__VA_ARGS__))                    \
    (obj, __VA_ARGS__, buf)

#define DEFINE_SERIALIZER(Type, ...)                                                               \
    template <std::endian DstEndian> struct Serializer<Type, DstEndian> {                          \
        static void serialize(const Type &obj, std::span<std::uint8_t> &buf)                                  \
        {                                                                                          \
            SERIALIZE_FIELDS(obj, buf, __VA_ARGS__);                                               \
        }                                                                                          \
    };

#define DESERIALIZE_FIELD(field, buf)                                                              \
    field = Deserializer<decltype(field), SrcEndian>::deserialize(buf);

// 支持1~9个字段的宏，每个宏递归调用前一个宏
#define DESERIALIZE_FIELDS_1(obj, f1, buf) DESERIALIZE_FIELD(obj.f1, buf);

#define DESERIALIZE_FIELDS_2(obj, f1, f2, buf)                                                     \
    DESERIALIZE_FIELDS_1(obj, f1, buf);                                                            \
    DESERIALIZE_FIELD(obj.f2, buf)

#define DESERIALIZE_FIELDS_3(obj, f1, f2, f3, buf)                                                 \
    DESERIALIZE_FIELDS_2(obj, f1, f2, buf);                                                        \
    DESERIALIZE_FIELD(obj.f3, buf)

#define DESERIALIZE_FIELDS_4(obj, f1, f2, f3, f4, buf)                                             \
    DESERIALIZE_FIELDS_3(obj, f1, f2, f3, buf);                                                    \
    DESERIALIZE_FIELD(obj.f4, buf)

#define DESERIALIZE_FIELDS_5(obj, f1, f2, f3, f4, f5, buf)                                         \
    DESERIALIZE_FIELDS_4(obj, f1, f2, f3, f4, buf);                                                \
    DESERIALIZE_FIELD(obj.f5, buf)

#define DESERIALIZE_FIELDS_6(obj, f1, f2, f3, f4, f5, f6, buf)                                     \
    DESERIALIZE_FIELDS_5(obj, f1, f2, f3, f4, f5, buf);                                            \
    DESERIALIZE_FIELD(obj.f6, buf)

#define DESERIALIZE_FIELDS_7(obj, f1, f2, f3, f4, f5, f6, f7, buf)                                 \
    DESERIALIZE_FIELDS_6(obj, f1, f2, f3, f4, f5, f6, buf);                                        \
    DESERIALIZE_FIELD(obj.f7, buf)

#define DESERIALIZE_FIELDS_8(obj, f1, f2, f3, f4, f5, f6, f7, f8, buf)                             \
    DESERIALIZE_FIELDS_7(obj, f1, f2, f3, f4, f5, f6, f7, buf);                                    \
    DESERIALIZE_FIELD(obj.f8, buf)

#define DESERIALIZE_FIELDS_9(obj, f1, f2, f3, f4, f5, f6, f7, f8, f9, buf)                         \
    DESERIALIZE_FIELDS_8(obj, f1, f2, f3, f4, f5, f6, f7, f8, buf);                                \
    DESERIALIZE_FIELD(obj.f9, buf)

#define DESERIALIZE_NUM_ARGS_IMPL(_1, _2, _3, _4, _5, _6, _7, _8, _9, N, ...) N
#define DESERIALIZE_NUM_ARGS(...) DESERIALIZE_NUM_ARGS_IMPL(__VA_ARGS__, 9, 8, 7, 6, 5, 4, 3, 2, 1)

#define DESERIALIZE_FIELDS_SELECT(NAME, NUM) NAME##NUM
#define DESERIALIZE_FIELDS_EXPAND(NAME, NUM) DESERIALIZE_FIELDS_SELECT(NAME, NUM)
#define DESERIALIZE_FIELDS(obj, buf, ...)                                                          \
    DESERIALIZE_FIELDS_EXPAND(DESERIALIZE_FIELDS_, DESERIALIZE_NUM_ARGS(__VA_ARGS__))              \
    (obj, __VA_ARGS__, buf)

#define DEFINE_DESERIALIZER(Type, ...)                                                             \
    template <std::endian SrcEndian> struct Deserializer<Type, SrcEndian> {                        \
        static Type deserialize(std::span<std::uint8_t> &buf)                                      \
        {                                                                                          \
            Type obj;                                                                              \
            DESERIALIZE_FIELDS(obj, buf, __VA_ARGS__);                                             \
            return obj;                                                                            \
        }                                                                                          \
    };

#define DEFINE_SERIALIZER_DESERIALIZER(Type, ...)                                                  \
    DEFINE_SERIALIZER(Type, __VA_ARGS__)                                                           \
    DEFINE_DESERIALIZER(Type, __VA_ARGS__)

// 基础类型特化
template <typename T, std::endian DstEndian> struct Serializer;
template <typename T, std::endian SrcEndian> struct Deserializer;

// uint16_t 特化

template <std::endian DstEndian> struct Serializer<std::uint16_t, DstEndian> {
    static void serialize(std::uint16_t value, std::span<std::uint8_t> &input)
    {
        auto buf = input;
        input = buf.subspan(sizeof(std::uint16_t));

        if constexpr (std::endian::native == DstEndian) {
            std::memcpy(buf.data(), &value, sizeof(value));
        } else {
            buf[0] = static_cast<std::uint8_t>(value >> 8);
            buf[1] = static_cast<std::uint8_t>(value & 0xFF);
        }
    }
};

template <std::endian SrcEndian> struct Deserializer<std::uint16_t, SrcEndian> {
    static std::uint16_t deserialize(std::span<std::uint8_t> &input)
    {
        auto buf = input;
        input = buf.subspan(sizeof(std::uint16_t));
        if constexpr (std::endian::native == SrcEndian) {
            std::uint16_t value;
            std::memcpy(&value, buf.data(), sizeof(value));
            return value;
        } else {
            return static_cast<std::uint16_t>(buf[0] << 8 | buf[1]);
        }
    }
};

// uint64_t 特化
template <std::endian DstEndian> struct Serializer<std::uint64_t, DstEndian> {
    static void serialize(std::uint64_t value, std::span<std::uint8_t> &input)
    {
        auto buf = input;
        input = buf.subspan(sizeof(std::uint64_t));

        if constexpr (std::endian::native == DstEndian) {
            std::memcpy(buf.data(), &value, sizeof(value));
        } else {
            buf[0] = static_cast<std::uint8_t>(value >> 56);
            buf[1] = static_cast<std::uint8_t>((value >> 48) & 0xFF);
            buf[2] = static_cast<std::uint8_t>((value >> 40) & 0xFF);
            buf[3] = static_cast<std::uint8_t>((value >> 32) & 0xFF);
            buf[4] = static_cast<std::uint8_t>((value >> 24) & 0xFF);
            buf[5] = static_cast<std::uint8_t>((value >> 16) & 0xFF);
            buf[6] = static_cast<std::uint8_t>((value >> 8) & 0xFF);
            buf[7] = static_cast<std::uint8_t>(value & 0xFF);
        }
    }
};
template <std::endian SrcEndian> struct Deserializer<std::uint64_t, SrcEndian> {
    static std::uint64_t deserialize(std::span<std::uint8_t> &input)
    {
        auto buf = input;
        input = buf.subspan(sizeof(std::uint64_t));
        if constexpr (std::endian::native == SrcEndian) {
            std::uint64_t value;
            std::memcpy(&value, buf.data(), sizeof(value));
            return value;
        } else {
            return static_cast<std::uint64_t>(buf[0]) << 56 |
                   static_cast<std::uint64_t>(buf[1]) << 48 |
                   static_cast<std::uint64_t>(buf[2]) << 40 |
                   static_cast<std::uint64_t>(buf[3]) << 32 |
                   static_cast<std::uint64_t>(buf[4]) << 24 |
                   static_cast<std::uint64_t>(buf[5]) << 16 |
                   static_cast<std::uint64_t>(buf[6]) << 8 | static_cast<std::uint64_t>(buf[7]);
        }
    }
};

// float_t 特化
template <std::endian DstEndian> struct Serializer<std::float_t, DstEndian> {
    static void serialize(std::float_t value, std::span<std::uint8_t> &input)
    {
        auto buf = input;
        input = buf.subspan(sizeof(std::uint64_t));

        static_assert(sizeof(std::float_t) == 4, "Only 4-byte float supported");
        if constexpr (std::endian::native == DstEndian) {
            std::memcpy(buf.data(), &value, sizeof(value));
        } else {
            std::uint32_t tmp;
            std::memcpy(&tmp, &value, sizeof(tmp));
            buf[0] = static_cast<std::uint8_t>(tmp >> 24);
            buf[1] = static_cast<std::uint8_t>((tmp >> 16) & 0xFF);
            buf[2] = static_cast<std::uint8_t>((tmp >> 8) & 0xFF);
            buf[3] = static_cast<std::uint8_t>(tmp & 0xFF);
        }
    }
};
template <std::endian SrcEndian> struct Deserializer<std::float_t, SrcEndian> {
    static std::float_t deserialize(std::span<std::uint8_t> &input)
    {
        auto buf = input;
        input = buf.subspan(sizeof(std::uint64_t));

        static_assert(sizeof(std::float_t) == 4, "Only 4-byte float supported");
        if constexpr (std::endian::native == SrcEndian) {
            std::float_t value;
            std::memcpy(&value, buf.data(), sizeof(value));
            return value;
        } else {
            std::uint32_t tmp = static_cast<std::uint32_t>(buf[0]) << 24 |
                                static_cast<std::uint32_t>(buf[1]) << 16 |
                                static_cast<std::uint32_t>(buf[2]) << 8 |
                                static_cast<std::uint32_t>(buf[3]);
            std::float_t value;
            std::memcpy(&value, &tmp, sizeof(value));
            return value;
        }
    }
};

// std::vector<T> 特化
template <typename T, std::endian DstEndian> struct Serializer<std::vector<T>, DstEndian> {
    static void serialize(const std::vector<T> &vec, std::span<std::uint8_t> &buf)
    {
        for (const auto &item : vec) {
            Serializer<T, DstEndian>::serialize(item, buf);
        }
    }
};

template <typename T, std::endian SrcEndian> struct Deserializer<std::vector<T>, SrcEndian> {
    static std::vector<T> deserialize(std::span<std::uint8_t> &input)
    {
        std::vector<T> vec;
        while (input.size() != 0) {
            vec.emplace_back(Deserializer<T, SrcEndian>::deserialize(input));
        }
        return std::move(vec);
    }
};
