#pragma once
#include <bit>
#include <cmath>
#include <cstdint>

#include "serialize.hpp"

struct TelegramHeader {
    std::uint16_t Magic;
    std::uint16_t Command;
    std::uint16_t Length;
    std::uint16_t Reverse1;
};
DEFINE_SERIALIZER_DESERIALIZER(TelegramHeader, Magic, Command, Length, Reverse1);


struct ReportTarget {
    std::uint16_t Azimuth;
    std::uint16_t Elevation;
    std::uint16_t Range;
    std::uint16_t Doppler;
    std::float_t Ampplitude;
    std::uint16_t SNR;
};
DEFINE_SERIALIZER_DESERIALIZER(ReportTarget, Azimuth, Elevation, Range, Doppler, Ampplitude, SNR);

struct ReportTargetInfo {
    std::uint64_t FrameSeq;
    std::uint16_t CPINum;
    std::uint16_t AzimuthBegin;   //  0-36000
    std::uint16_t ElevationBegin; //-18000--180000
    std::uint16_t TargetNum;      // 0-65535
};
DEFINE_SERIALIZER_DESERIALIZER(ReportTargetInfo, FrameSeq, CPINum, AzimuthBegin, ElevationBegin,
                               TargetNum);

struct ReportTargetCmd {
    ReportTargetInfo Info;
    std::vector<ReportTarget> Targets; // pod 不定长数组，用于直接解析报文
};
DEFINE_SERIALIZER_DESERIALIZER(ReportTargetCmd, Info, Targets);