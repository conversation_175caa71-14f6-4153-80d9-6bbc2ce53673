#pragma once
#include <thread>
#include <vector>

#include "logger.hpp"
#include "node_base.hpp"

namespace DataFlow
{
template <typename Queue> class Framework final
{
  private:
    using NodeSPtr = ProcessNode<Queue>::SPtr;
    using QueueSPtr = Queue::SPtr;
    using QueueType = Queue;

  public:
    Framework() = default;
    ~Framework() = default;

    Framework(const Framework &) = delete;
    Framework &operator=(const Framework &) = delete;
    Framework(Framework &&) = delete;
    Framework &operator=(Framework &&) = delete;

  public:
    int Connect(NodeSPtr up, NodeSPtr down, std::size_t upIdx, std::size_t downIdx,
                std::string name, std::size_t size)
    {
        auto queue = QueueSPtr{new QueueType{size}};
        auto ok = up->ConnectOutput(queue, upIdx);
        if (!ok) {
            return -1;
        }
        ok = down->ConnectInput(queue, downIdx);
        if (!ok) {
            return -1;
        }

        queues_[name] = queue;
        queue_names_.push_back(name);

        if (std::find(nodes_.begin(), nodes_.end(), up) == nodes_.end()) {
            nodes_.push_back(up);
        }
        if (std::find(nodes_.begin(), nodes_.end(), down) == nodes_.end()) {
            nodes_.push_back(down);
        }

        return 0;
    }

    int FanIn(std::vector<NodeSPtr> up, NodeSPtr down, std::size_t upIdx, std::size_t downIdx,
              std::string name, std::size_t size)
    {
        auto queue = QueueSPtr{new QueueType{size}};
        for (std::size_t i = 0; i < up.size(); ++i) {
            auto ok = up[i]->ConnectOutput(queue, upIdx);
            if (!ok) {
                return -1;
            }
        }

        auto ok = down->ConnectInput(queue, downIdx);
        if (!ok) {
            return -1;
        }

        queues_[name] = queue;
        queue_names_.push_back(name);

        for (auto &node : up) {
            if (std::find(nodes_.begin(), nodes_.end(), node) == nodes_.end()) {
                nodes_.push_back(node);
            }
        }

        if (std::find(nodes_.begin(), nodes_.end(), down) == nodes_.end()) {
            nodes_.push_back(down);
        }

        return 0;
    }

    int FanOut(NodeSPtr up, std::vector<NodeSPtr> down, std::size_t upIdx, std::size_t downIdx,
               std::string name, std::size_t size)
    {
        auto queue = QueueSPtr{new QueueType{size}};
        auto ok = up->ConnectOutput(queue, upIdx);
        if (!ok) {
            return -1;
        }

        for (std::size_t i = 0; i < down.size(); ++i) {
            ok = down[i]->ConnectInput(queue, downIdx);
            if (!ok) {
                return -1;
            }
        }

        queues_[name] = queue;
        queue_names_.push_back(name);

        if (std::find(nodes_.begin(), nodes_.end(), up) == nodes_.end()) {
            nodes_.push_back(up);
        }

        for (auto &node : down) {
            if (std::find(nodes_.begin(), nodes_.end(), node) == nodes_.end()) {
                nodes_.push_back(node);
            }
        }

        return 0;
    }

    int FanToFan(std::vector<NodeSPtr> up, std::vector<NodeSPtr> down, std::size_t upIdx,
                 std::size_t downIdx, std::string name, std::size_t size)
    {
        auto queue = QueueSPtr{new QueueType{size}};
        for (std::size_t i = 0; i < up.size(); ++i) {
            auto ok = up[i]->ConnectOutput(queue, upIdx);
            if (!ok) {
                return -1;
            }
        }
        for (std::size_t i = 0; i < down.size(); ++i) {
            auto ok = down[i]->ConnectInput(queue, downIdx);
            if (!ok) {
                return -1;
            }
        }

        queues_[name] = queue;
        queue_names_.push_back(name);

        for (auto &node : up) {
            if (std::find(nodes_.begin(), nodes_.end(), node) == nodes_.end()) {
                nodes_.push_back(node);
            }
        }
        for (auto &node : down) {
            if (std::find(nodes_.begin(), nodes_.end(), node) == nodes_.end()) {
                nodes_.push_back(node);
            }
        }

        return 0;
    }

    int Start()
    {
        for (auto &node : nodes_) {
            node->Start();
        }

        thread_ = std::thread(&Framework::_loop, this);
        SetThreadName(thread_, "FrameworkMon");
        return 0;
    }

    int Join()
    {
        for (auto &node : nodes_) {
            node->Join();
        }
        return 0;
    }

    void Close()
    {
        for (auto [key, queue] : queues_) {
            queue->Close();
        }
    }

  private:
    void _loop()
    {
        while (exitFlag_.load() == false) {
            std::ostringstream ss;
            ss << "status:" << std::endl;
            for (auto name : queue_names_) {
                auto queue = queues_[name];
                auto result = queue->status();
                for (auto [key, size] : result) {
                    ss << name << "." << key << ": " << size << std::endl;
                }
            }

            for (auto &node : nodes_) {
                auto result = node->Status();
                if (result.empty()) {
                    continue;
                }
                for (auto [key, size] : result) {
                    ss << key << ": " << size << std::endl;
                }
            }

            Logger::Get().info(ss.str());
            std::this_thread::sleep_for(std::chrono::milliseconds(2000));
        }
    }

    std::atomic<bool> exitFlag_{false};
    std::map<std::string, QueueSPtr> queues_;
    std::vector<std::string> queue_names_;
    std::vector<NodeSPtr> nodes_;
    std::thread thread_;
};
} // namespace DataFlow