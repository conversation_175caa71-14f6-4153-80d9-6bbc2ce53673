#pragma once

#include <iostream>
#include <memory>
#include <mutex>
#include <set>
#include <string>
#include <string_view>

#include <asio.hpp>

using asio::ip::tcp;

template <int cacheSize = 1024>
class Session : public std::enable_shared_from_this<Session<cacheSize>>
{
  public:
    using SPtr = std::shared_ptr<Session>;
    using Base = std::enable_shared_from_this<Session<cacheSize>>;
    using disconnect_cb = std::function<void(SPtr)>;
    using connect_cb = std::function<void(SPtr)>;
    using read_cb = std::function<void(SPtr, std::vector<uint8_t> &&)>;
    using write_cb = std::function<void(SPtr)>;

    Session(tcp::socket socket, connect_cb ccb, disconnect_cb dcb, read_cb rcb, write_cb wcb)
        : socket_(std::move(socket)), on_connect_(ccb), on_disconnect_(dcb), on_read_(rcb),
          on_write_(wcb)
    {
    }

    void start()
    {
        if (on_connect_)
            on_connect_(Base::shared_from_this());
    }

    ~Session() {}

    void stop()
    {
        if (socket_.is_open()) {
            socket_.close();
            if (on_disconnect_)
                on_disconnect_(Base::shared_from_this());

            std::cout << "Session stopped." << std::endl;
        } else {
            std::cout << "Session already closed." << std::endl;
        }
    }
    void read(size_t needSize)
    {
        auto self = Base::shared_from_this();
        socket_.async_read_some(
            asio::buffer(data_, needSize),
            [this, self, needSize](std::error_code ec, std::size_t length) {
                if (ec) {
                    // 处理写入错误，自动断开连接
                    std::cerr << "Read error: " << ec.message() << std::endl;
                    self->stop();
                    return;
                }
                if (length != needSize) {
                    std::cerr << "Read error: length != size" << std::endl;
                    self->stop();
                    return;
                }

                std::cout << "Received: " << std::string_view((char *) data_.data(), length)
                          << std::endl;
                this->on_read_(self, std::vector<uint8_t>(data_.data(), data_.data() + length));
            });
    }

    void write(std::vector<uint8_t> &&data)
    {
        auto self = Base::shared_from_this();
        asio::async_write(socket_, asio::buffer(data, data.size()),
                          [this, self](std::error_code ec, std::size_t /*length*/) {
                              if (ec) {
                                  // 处理写入错误，自动断开连接
                                  std::cerr << "Write error: " << ec.message() << std::endl;
                                  self->stop();
                                  return;
                              }
                              this->on_write_(self);
                          });
    }

    void write(std::vector<uint8_t> &data)
    {
        auto self = Base::shared_from_this();
        asio::async_write(socket_, asio::buffer(data, data.size()),
                          [this, self](std::error_code ec, std::size_t /*length*/) {
                              if (ec) {
                                  // 处理写入错误，自动断开连接
                                  std::cerr << "Write error: " << ec.message() << std::endl;
                                  self->stop();
                                  return;
                              }
                              this->on_write_(self);
                          });
    }

  private:
    tcp::socket socket_;
    std::array<uint8_t, cacheSize> data_;
    disconnect_cb on_disconnect_;
    connect_cb on_connect_;
    read_cb on_read_;
    write_cb on_write_;
};

// 支持客户端管理和自定义回调的 TCPServer
template <std::size_t MaxClients, int cacheSize> class TCPServer
{
  public:
    using SType = Session<cacheSize>;
    using SSPtr = Session<cacheSize>::SPtr;
    using ClientsCallback = std::function<void(std::set<SSPtr> &, std::mutex &)>;

    TCPServer(asio::io_context &io_context, short port, typename SType::connect_cb ccb,
              typename SType::disconnect_cb dcb, typename SType::read_cb rcb,
              typename SType::write_cb wcb)
        : acceptor_(io_context, tcp::endpoint(tcp::v4(), port)), socket_(io_context), ccb_(ccb),
          dcb_(dcb), rcb_(rcb), wcb_(wcb)
    {
        do_accept();
    }

    // 提供一个安全访问 clients 的回调接口
    void WithClient(const ClientsCallback &cb)
    {
        std::lock_guard<std::mutex> lock(clientsMutex_);
        cb(clients_, clientsMutex_);
    }

  private:
    void do_accept()
    {
        acceptor_.async_accept(socket_, [this](std::error_code ec) {
            if (!ec) {
                std::lock_guard<std::mutex> lock(clientsMutex_);
                if (clients_.size() < MaxClients) {
                    auto client = std::make_shared<SType>(
                        std::move(socket_), ccb_,
                        [this](SSPtr s) {
                            std::lock_guard<std::mutex> lock(clientsMutex_);
                            clients_.erase(s);
                            dcb_(s);
                        },
                        rcb_, wcb_);
                    clients_.insert(client);
                    client->start();
                } else {
                    // 超过最大客户端数，关闭连接
                    socket_.close();
                }
            }
            do_accept();
        });
    }

    tcp::acceptor acceptor_;
    tcp::socket socket_;
    std::set<SSPtr> clients_;
    std::mutex clientsMutex_;
    SType::connect_cb ccb_;
    SType::disconnect_cb dcb_;
    SType::read_cb rcb_;
    SType::write_cb wcb_;
};