import socket
import time
import threading

SERVER_IP = '127.0.0.1'
SERVER_PORT = 6868

def send_data(sock):
    while True:
        sock.sendall(b'hello world')
        time.sleep(1)

def recv_data(sock):
    while True:
        data = sock.recv(1024)
        if not data:
            break
        print('Received:', data.decode())

def main():
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.connect((SERVER_IP, SERVER_PORT))
        threading.Thread(target=send_data, args=(s,), daemon=True).start()
        recv_data(s)

if __name__ == '__main__':
    main()